"""
Main entry point for the Data Analysis Tool.

This module provides entry points for different modes of operation:
- API server
- Frontend
- CLI
"""

import sys
import typer
import uvicorn
from typing import Optional

app = typer.Typer()

@app.command()
def api(host: str = "0.0.0.0", port: int = 8000, reload: bool = True):
    """Start the API server."""
    from backend.main import app as api_app
    uvicorn.run("backend.main:app", host=host, port=port, reload=reload)

@app.command()
def frontend(port: int = 8501):
    """Start the Streamlit frontend."""
    import streamlit.web.cli as stcli
    import os
    
    # Change to the frontend directory
    frontend_path = os.path.join(os.path.dirname(__file__), "frontend", "app.py")
    sys.argv = ["streamlit", "run", frontend_path, "--server.port", str(port)]
    stcli.main()

@app.command()
def cli(file: str = typer.Argument(None, help="Input CSV file path"),
        output: Optional[str] = typer.Option(None, "--output", "-o", help="Output file path (.csv or .json)"),
        include: Optional[list[str]] = typer.Option(None, "--include", "-i", help="Columns to include"),
        exclude: Optional[list[str]] = typer.Option(None, "--exclude", "-e", help="Columns to exclude")):
    """Process CSV files with schema rows using the command line."""
    from src.cli.commands import process_csv_file
    
    if file:
        df = process_csv_file(file, output, include, exclude)
        
        # Display processed data
        if df is not None:
            print("\n=== Processed Data Preview ===")
            print(df.head())
    else:
        # If no file is provided, show help
        typer.echo("Please provide a CSV file path.")
        typer.echo("Example: python -m src.main cli data.csv -o output.csv")

def main():
    """Main entry point for the application."""
    if len(sys.argv) > 1 and sys.argv[1] in ["api", "frontend", "cli"]:
        app()
    else:
        # Default to API if no command is provided
        sys.argv.insert(1, "api")
        app()

if __name__ == "__main__":
    main()
