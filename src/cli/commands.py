"""
CLI commands for the Data Analysis Tool.

This module provides command-line functionality for processing CSV files with schema rows.
"""

import argparse
import sys
import json
import pandas as pd
from typing import List, Dict, Any, Optional, Union


class CSVSchemaProcessor:
    """
    Process CSV files with schema rows in the header.
    
    This processor handles CSV files with special schema format where:
    - First 4 lines contain schema information
    - Remaining lines contain data
    """
    
    def __init__(self, csv_content=None, file_path=None):
        """
        Initialize the processor with either CSV content or a file path.
        
        Args:
            csv_content (str, optional): Raw CSV content as string
            file_path (str, optional): Path to CSV file
        """
        self.raw_lines = []
        self.schema_rows = []
        self.data_rows = []
        self.detected_schema = []
        self.processed_data = None
        
        if file_path:
            self.load_from_file(file_path)
        elif csv_content:
            self.load_from_string(csv_content)

    def load_from_file(self, file_path):
        """Load CSV data from a file"""
        with open(file_path, 'r') as f:
            self.raw_lines = [line.strip() for line in f.readlines() if line.strip()]
        self._parse_raw_data()

    def load_from_string(self, csv_content):
        """Load CSV data from a string"""
        self.raw_lines = [line.strip() for line in csv_content.splitlines() if line.strip()]
        self._parse_raw_data()

    def _parse_raw_data(self):
        """Parse raw CSV data to extract schema and data rows"""
        if len(self.raw_lines) < 4:
            raise ValueError("CSV doesn't contain enough rows for schema information")
        
        # First 4 lines are schema info
        self.schema_rows = self.raw_lines[:4]
        
        # Remaining lines are data rows
        self.data_rows = self.raw_lines[4:]
        
        # Detect schema
        self._detect_schema()

    def _detect_schema(self):
        """Detect the schema based on header and datatype rows"""
        datatype_row = self.schema_rows[1].split(',')
        header_row = self.schema_rows[3].split(',')
        
        self.detected_schema = []
        for i, header in enumerate(header_row):
            datatype = datatype_row[i] if i < len(datatype_row) else 'unknown'
            
            self.detected_schema.append({
                'column_index': i,
                'name': header,
                'datatype': datatype,
                'include': header != "result",  # Default to exclude 'result' column
                'convert': True  # Default to converting values
            })

    def get_raw_preview(self, max_rows=5):
        """Get a preview of the raw data"""
        return {
            'schema_rows': self.schema_rows,
            'data_preview': self.data_rows[:max_rows]
        }

    def process_data(self, custom_schema=None):
        """
        Process the CSV data based on the provided schema.
        
        Args:
            custom_schema (list, optional): Custom schema configuration.
                                           If None, uses detected schema.
        
        Returns:
            pandas.DataFrame: Processed data as a DataFrame
        """
        schema = custom_schema if custom_schema is not None else self.detected_schema
        
        # Filter included columns
        filtered_schema = [col for col in schema if col.get('include', True)]
        
        records = []
        for row in self.data_rows:
            row_values = row.split(',')
            record = {}
            
            for col in filtered_schema:
                idx = col.get('column_index')
                if idx < len(row_values):
                    value = row_values[idx]
                    
                    # Apply type conversion if enabled
                    if col.get('convert', True):
                        datatype = col.get('datatype')
                        
                        if datatype == 'long':
                            try:
                                value = int(value)
                            except (ValueError, TypeError):
                                value = 0
                        elif datatype == 'double':
                            try:
                                value = float(value)
                            except (ValueError, TypeError):
                                value = 0.0
                        elif datatype.startswith('dateTime'):
                            try:
                                value = pd.to_datetime(value)
                            except:
                                value = None
                        elif datatype == 'boolean':
                            value = value.lower() == 'true'
                    
                    record[col.get('name')] = value
            
            records.append(record)
        
        self.processed_data = pd.DataFrame(records)
        return self.processed_data

    def export_to_json(self, file_path=None):
        """
        Export processed data to JSON.
        
        Args:
            file_path (str, optional): Path to save JSON file.
                                      If None, returns JSON string.
        
        Returns:
            str or None: JSON string if file_path is None
        """
        if self.processed_data is None:
            raise ValueError("No processed data available. Run process_data() first.")
        
        # Convert datetime objects to ISO format strings
        records = self.processed_data.to_dict(orient='records')
        for record in records:
            for key, value in record.items():
                if isinstance(value, pd.Timestamp):
                    record[key] = value.isoformat()
        
        if file_path:
            with open(file_path, 'w') as f:
                json.dump(records, f, indent=2)
        else:
            return json.dumps(records, indent=2)

    def export_to_csv(self, file_path=None):
        """
        Export processed data to CSV.
        
        Args:
            file_path (str, optional): Path to save CSV file.
                                      If None, returns CSV string.
        
        Returns:
            str or None: CSV string if file_path is None
        """
        if self.processed_data is None:
            raise ValueError("No processed data available. Run process_data() first.")
        
        if file_path:
            self.processed_data.to_csv(file_path, index=False)
        else:
            return self.processed_data.to_csv(index=False)


def process_csv_file(file_path, output_path=None, include_columns=None, exclude_columns=None):
    """
    Process a CSV file with schema rows.
    
    Args:
        file_path (str): Path to the input CSV file
        output_path (str, optional): Path to save the output file (.csv or .json)
        include_columns (list, optional): List of column names to include
        exclude_columns (list, optional): List of column names to exclude
        
    Returns:
        pandas.DataFrame: Processed data as a DataFrame
    """
    try:
        # Initialize processor
        processor = CSVSchemaProcessor(file_path=file_path)
        
        # Modify schema based on include/exclude columns
        schema = processor.detected_schema.copy()
        
        if include_columns:
            for col in schema:
                col['include'] = col['name'] in include_columns
        
        if exclude_columns:
            for col in schema:
                if col['name'] in exclude_columns:
                    col['include'] = False
        
        # Process data with modified schema
        df = processor.process_data(schema)
        
        # Export to file if output path is provided
        if output_path:
            if output_path.endswith('.json'):
                processor.export_to_json(output_path)
                print(f"Data exported to JSON: {output_path}")
            else:
                processor.export_to_csv(output_path)
                print(f"Data exported to CSV: {output_path}")
        
        return df
    
    except Exception as e:
        print(f"Error processing CSV file: {str(e)}")
        return None


def main():
    """Run the GUI interface for CSV processing."""
    print("GUI mode not implemented yet. Use command-line arguments instead.")
    print("Example: python -m src.cli.commands file.csv -o output.csv")


if __name__ == "__main__":
    if len(sys.argv) > 1:
        # Command-line mode
        parser = argparse.ArgumentParser(description="Process CSV files with schema rows")
        parser.add_argument("file", help="Input CSV file path")
        parser.add_argument("-o", "--output", help="Output file path (.csv or .json)")
        parser.add_argument("-i", "--include", nargs="+", help="Columns to include")
        parser.add_argument("-e", "--exclude", nargs="+", help="Columns to exclude")
        
        args = parser.parse_args()
        
        df = process_csv_file(
            args.file,
            args.output,
            args.include,
            args.exclude
        )
        
        # Display processed data
        if df is not None:
            print("\n=== Processed Data Preview ===")
            print(df.head())
    else:
        # GUI mode
        main()
