"""
Main entry point for the CLI module.

This allows running the CLI directly with `python -m src.cli`.
"""

import sys
import argparse
from src.cli.commands import process_csv_file, main

if __name__ == "__main__":
    if len(sys.argv) > 1:
        # Command-line mode
        parser = argparse.ArgumentParser(description="Process CSV files with schema rows")
        parser.add_argument("file", help="Input CSV file path")
        parser.add_argument("-o", "--output", help="Output file path (.csv or .json)")
        parser.add_argument("-i", "--include", nargs="+", help="Columns to include")
        parser.add_argument("-e", "--exclude", nargs="+", help="Columns to exclude")
        
        args = parser.parse_args()
        
        df = process_csv_file(
            args.file,
            args.output,
            args.include,
            args.exclude
        )
        
        # Display processed data
        if df is not None:
            print("\n=== Processed Data Preview ===")
            print(df.head())
    else:
        # GUI mode
        main()
