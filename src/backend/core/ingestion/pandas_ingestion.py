
import pandas as pd
import numpy as np
import os
import json
from typing import Any, Dict, List, Optional
import gc
from src.backend.core.ingestion.base import DataIngestionBase


class CSVSchemaProcessor:
    """
    Process CSV files with schema rows in the header.

    This processor handles CSV files with special format where:
    - First 4 lines contain schema information
    - Remaining lines contain data
    """

    def __init__(self, csv_content=None, file_path=None):
        """
        Initialize the processor with either CSV content or a file path.

        Args:
            csv_content (str, optional): Raw CSV content as string
            file_path (str, optional): Path to CSV file
        """
        self.raw_lines = []
        self.schema_rows = []
        self.data_rows = []
        self.detected_schema = []
        self.processed_data = None

        if file_path:
            self.load_from_file(file_path)
        elif csv_content:
            self.load_from_string(csv_content)

    def load_from_file(self, file_path):
        """Load CSV data from a file"""
        with open(file_path, 'r') as f:
            self.raw_lines = [line.strip() for line in f.readlines() if line.strip()]
        self._parse_raw_data()

    def load_from_string(self, csv_content):
        """Load CSV data from a string"""
        self.raw_lines = [line.strip() for line in csv_content.splitlines() if line.strip()]
        self._parse_raw_data()

    def _parse_raw_data(self):
        """Parse raw CSV data to extract schema and data rows"""
        if len(self.raw_lines) < 4:
            raise ValueError("CSV doesn't contain enough rows for schema information")

        # First 4 lines are schema info
        self.schema_rows = self.raw_lines[:4]

        # Remaining lines are data rows
        self.data_rows = self.raw_lines[4:]

        # Detect schema
        self._detect_schema()

    def _detect_schema(self):
        """Detect the schema based on header and datatype rows"""
        datatype_row = self.schema_rows[1].split(',')
        header_row = self.schema_rows[3].split(',')

        self.detected_schema = []
        for i, header in enumerate(header_row):
            datatype = datatype_row[i] if i < len(datatype_row) else 'unknown'

            self.detected_schema.append({
                'column_index': i,
                'name': header,
                'datatype': datatype,
                'include': header != "result",  # Default to exclude 'result' column
                'convert': True  # Default to converting values
            })

    def get_raw_preview(self, max_rows=5):
        """Get a preview of the raw data"""
        return {
            'schema_rows': self.schema_rows,
            'data_preview': self.data_rows[:max_rows]
        }

    def process_data(self, custom_schema=None):
        """
        Process the CSV data based on the provided schema.

        Args:
            custom_schema (list, optional): Custom schema configuration.
                                           If None, uses detected schema.

        Returns:
            pandas.DataFrame: Processed data as a DataFrame
        """
        schema = custom_schema if custom_schema is not None else self.detected_schema

        # Filter included columns
        filtered_schema = [col for col in schema if col.get('include', True)]

        records = []
        for row in self.data_rows:
            row_values = row.split(',')
            record = {}

            for col in filtered_schema:
                idx = col.get('column_index')
                if idx < len(row_values):
                    value = row_values[idx]

                    # Apply type conversion if enabled
                    if col.get('convert', True):
                        datatype = col.get('datatype')

                        if datatype == 'long':
                            try:
                                value = int(value)
                            except (ValueError, TypeError):
                                value = 0
                        elif datatype == 'double':
                            try:
                                value = float(value)
                            except (ValueError, TypeError):
                                value = 0.0
                        elif datatype.startswith('dateTime'):
                            try:
                                value = pd.to_datetime(value)
                            except:
                                value = None
                        elif datatype == 'boolean':
                            value = value.lower() == 'true'

                record[col.get('name')] = value

            records.append(record)

        self.processed_data = pd.DataFrame(records)
        return self.processed_data

    def export_to_json(self, file_path=None):
        """
        Export processed data to JSON.

        Args:
            file_path (str, optional): Path to save JSON file.
                                      If None, returns JSON string.

        Returns:
            str or None: JSON string if file_path is None
        """
        if self.processed_data is None:
            raise ValueError("No processed data available. Run process_data() first.")

        # Convert datetime objects to ISO format strings
        records = self.processed_data.to_dict(orient='records')
        for record in records:
            for key, value in record.items():
                if isinstance(value, pd.Timestamp):
                    record[key] = value.isoformat()

        if file_path:
            with open(file_path, 'w') as f:
                json.dump(records, f, indent=2)
        else:
            return json.dumps(records, indent=2)

    def export_to_csv(self, file_path=None):
        """
        Export processed data to CSV.

        Args:
            file_path (str, optional): Path to save CSV file.
                                      If None, returns CSV string.

        Returns:
            str or None: CSV string if file_path is None
        """
        if self.processed_data is None:
            raise ValueError("No processed data available. Run process_data() first.")

        if file_path:
            self.processed_data.to_csv(file_path, index=False)
        else:
            return self.processed_data.to_csv(index=False)


class PandasIngestion(DataIngestionBase):
    """
    Pandas implementation of data ingestion strategy with memory optimization.

    This class provides methods to efficiently load different file types
    into pandas DataFrames while optimizing memory usage and handling
    large datasets appropriately.
    """

    def __init__(self):
        """Initialize with optimized settings for string inference"""
        # Enable string inference with PyArrow for better memory efficiency
        pd.options.future.infer_string = True

    def load_data(self, file_path: str, file_type: str, **kwargs) -> pd.DataFrame:
        """
        Load data using pandas with memory optimization.

        Args:
            file_path: Path to the file to load
            file_type: Type of file (csv, excel, json, parquet)
            **kwargs: Additional arguments for pandas reader

        Returns:
            Memory-optimized pandas DataFrame

        Raises:
            ValueError: If file type is not supported
        """
        # Set default memory optimization parameters if not provided
        optimize_memory = kwargs.pop('optimize_memory', True)
        use_chunking = kwargs.pop('use_chunking', False)
        chunk_size = kwargs.pop('chunk_size', 100000)

        # Choose appropriate loading method based on file type
        if file_type == 'csv':
            result = self._load_csv(file_path, use_chunking, chunk_size, **kwargs)
        elif file_type == 'excel':
            result = self._load_excel(file_path, **kwargs)
        elif file_type == 'json':
            result = self._load_json(file_path, **kwargs)
        elif file_type == 'parquet':
            result = self._load_parquet(file_path, **kwargs)
        else:
            raise ValueError(f"Unsupported file type for pandas ingestion: {file_type}")

        # Apply memory optimization if requested
        if optimize_memory:
            result = self._optimize_dtypes(result)

        # Force garbage collection to free memory
        gc.collect()

        return result

    def _load_csv(self, file_path: str, use_chunking: bool, chunk_size: int, **kwargs) -> pd.DataFrame:
        """
        Load CSV file with appropriate optimizations.

        For large files, uses chunking and dtype inference to minimize memory usage.

        Args:
            file_path: Path to the CSV file
            use_chunking: Whether to use chunking for large files
            chunk_size: Size of chunks when using chunking
            **kwargs: Additional arguments for pd.read_csv

        Returns:
            Loaded pandas DataFrame
        """
        # Check if this is a special format CSV with metadata comments
        if self._is_special_format_csv(file_path):
            return self._load_special_format_csv(file_path, **kwargs)

        # First determine file size to choose appropriate loading strategy
        file_size = os.path.getsize(file_path)

        # For small files or when chunking is disabled
        if file_size < 1000 * 1024 * 1024 or not use_chunking:  # Less than 1GB
            # Use PyArrow engine for better performance
            if 'engine' not in kwargs:
                kwargs['engine'] = 'pyarrow'

            # Enable low_memory mode for better memory usage
            if 'low_memory' not in kwargs:
                kwargs['low_memory'] = True

            return pd.read_csv(file_path, **kwargs)

        # For large files, use chunking with PyArrow
        chunks = []
        for chunk in pd.read_csv(file_path, chunksize=chunk_size, engine='pyarrow', **kwargs):
            if not chunks:  # First chunk
                dtypes = self._infer_optimal_dtypes(chunk)
            else:
                # Apply optimal dtypes to subsequent chunks
                for col, dtype in dtypes.items():
                    if col in chunk.columns:
                        try:
                            chunk[col] = chunk[col].astype(dtype)
                        except (ValueError, TypeError):
                            pass
            chunks.append(chunk)

        result = pd.concat(chunks, ignore_index=True)
        chunks.clear()
        return result

    def _is_special_format_csv(self, file_path: str) -> bool:
        """
        Check if the CSV file has special format with metadata comments.

        Args:
            file_path: Path to the CSV file

        Returns:
            True if the file has special format, False otherwise
        """
        try:
            with open(file_path, 'r') as f:
                # Read first few lines to check for metadata comments
                for i in range(5):  # Check first 5 lines
                    line = f.readline().strip()
                    if line.startswith('#group') or line.startswith('#datatype') or line.startswith('#default'):
                        return True
                    if i >= 4:  # If we've read 5 lines and found no metadata
                        break
            return False
        except Exception:
            return False

    def _load_special_format_csv(self, file_path: str, **kwargs) -> pd.DataFrame:
        """
        Load a CSV file with special format (like InfluxDB Line Protocol).

        This handles files with metadata comments like:
        #group TRUE FALSE
        #datatype string long
        #default _result

        Args:
            file_path: Path to the CSV file
            **kwargs: Additional arguments for pd.read_csv

        Returns:
            Loaded pandas DataFrame with appropriate data types
        """
        # First, check if this is a standard CSV with index column but no metadata
        try:
            # Try to read the first few lines to check the format
            with open(file_path, 'r') as f:
                first_line = f.readline().strip()

                # If the first line starts with a comma or has column headers
                if first_line.startswith(',') or ('_time' in first_line and '_value' in first_line):
                    # This looks like a standard CSV with an index column
                    df = pd.read_csv(file_path, **kwargs)

                    # Convert datetime columns
                    for col in df.columns:
                        if '_time' in col or 'time' in col.lower() or 'date' in col.lower() or 'start' in col.lower() or 'stop' in col.lower():
                            try:
                                # Try to parse with UTC timezone for ISO format dates
                                if any(x in str(df[col].iloc[0]) for x in ['T', 'Z', '+00:00']):
                                    df[col] = pd.to_datetime(df[col], errors='coerce', utc=True)
                                else:
                                    df[col] = pd.to_datetime(df[col], errors='coerce')
                            except:
                                pass

                    # Convert numeric columns
                    for col in df.columns:
                        if col == '_value' or 'value' in col.lower() or 'amount' in col.lower():
                            try:
                                df[col] = pd.to_numeric(df[col], errors='coerce')
                            except:
                                pass

                    return df
        except:
            # If there's an error, fall back to the special format parsing
            pass

        # Try to use the CSVSchemaProcessor for files with schema rows
        try:
            # Check if this is a CSV with schema rows (first 4 lines)
            processor = CSVSchemaProcessor(file_path=file_path)

            # Process the data with the detected schema
            return processor.process_data()
        except ValueError:
            # If it's not a schema-based CSV, fall back to the original method
            pass

        # Parse metadata and data
        metadata = {}
        data_lines = []
        column_names = []

        with open(file_path, 'r') as f:
            for line in f:
                line = line.strip()
                if not line:
                    continue

                if line.startswith('#'):
                    # Parse metadata line
                    parts = line.split('\t')
                    key = parts[0][1:]  # Remove the # prefix
                    values = parts[1:]
                    metadata[key] = values

                    # If this is the header line, extract column names
                    if key == 'datatype':
                        column_names = values
                else:
                    # This is a data line
                    data_lines.append(line)

        # If we have column names from metadata, use them
        if column_names and 'names' not in kwargs:
            # Make sure column names are unique
            unique_names = []
            seen = set()
            for i, name in enumerate(column_names):
                if name in seen:
                    unique_names.append(f"{name}_{i}")
                else:
                    unique_names.append(name)
                    seen.add(name)
            kwargs['names'] = unique_names

        # If we have metadata, process it
        if metadata:
            # Create a temporary file with just the data lines
            import tempfile
            with tempfile.NamedTemporaryFile(mode='w', delete=False) as temp_file:
                temp_file_path = temp_file.name
                for line in data_lines:
                    temp_file.write(line + '\n')

            try:
                # Read the data using pandas
                df = pd.read_csv(temp_file_path, sep='\t', **kwargs)

                # Apply data types if specified in metadata
                if 'datatype' in metadata and len(metadata['datatype']) == len(df.columns):
                    for i, dtype_str in enumerate(metadata['datatype']):
                        col = df.columns[i]

                        # Convert to appropriate data type
                        if dtype_str == 'string':
                            df[col] = df[col].astype(str)
                        elif dtype_str == 'long' or dtype_str == 'integer':
                            df[col] = pd.to_numeric(df[col], errors='coerce')
                        elif dtype_str.startswith('dateTime'):
                            df[col] = pd.to_datetime(df[col], errors='coerce')
                        elif dtype_str == 'double' or dtype_str == 'float':
                            df[col] = pd.to_numeric(df[col], errors='coerce', downcast='float')
                        elif dtype_str == 'boolean':
                            df[col] = df[col].map({'TRUE': True, 'FALSE': False, True: True, False: False})

                return df

            finally:
                # Clean up the temporary file
                try:
                    os.unlink(temp_file_path)
                except:
                    pass
        else:
            # If no metadata was found, try to load as a regular CSV
            return pd.read_csv(file_path, **kwargs)

    def _load_excel(self, file_path: str, **kwargs) -> pd.DataFrame:
        """
        Load Excel file with appropriate optimizations.

        Args:
            file_path: Path to the Excel file
            **kwargs: Additional arguments for pd.read_excel

        Returns:
            Loaded pandas DataFrame
        """
        if 'engine' not in kwargs:
            kwargs['engine'] = 'openpyxl' if file_path.endswith('.xlsx') else 'xlrd'

        # Load the Excel file
        df = pd.read_excel(file_path, **kwargs)

        # Check if this might be a special format with metadata in the first few rows
        if len(df) > 3 and df.iloc[0:3].apply(lambda row: row.astype(str).str.startswith('#').any(), axis=1).all():
            # This looks like it might have metadata rows
            try:
                # Extract metadata
                metadata = {}
                data_start_row = 0

                for i in range(min(5, len(df))):
                    row = df.iloc[i]
                    first_cell = str(row.iloc[0])

                    if first_cell.startswith('#'):
                        # This is a metadata row
                        key = first_cell[1:]  # Remove the # prefix
                        values = row.iloc[1:].tolist()
                        metadata[key] = values
                        data_start_row = i + 1
                    else:
                        # This is not a metadata row, so we've reached the data
                        break

                if metadata and data_start_row > 0:
                    # Re-read the Excel file, skipping metadata rows
                    df = pd.read_excel(file_path, skiprows=data_start_row, **kwargs)

                    # Apply data types if specified in metadata
                    if 'datatype' in metadata and len(metadata['datatype']) == len(df.columns):
                        for i, dtype_str in enumerate(metadata['datatype']):
                            if i >= len(df.columns):
                                continue

                            col = df.columns[i]

                            # Convert to appropriate data type
                            if dtype_str == 'string':
                                df[col] = df[col].astype(str)
                            elif dtype_str == 'long' or dtype_str == 'integer':
                                df[col] = pd.to_numeric(df[col], errors='coerce')
                            elif dtype_str.startswith('dateTime'):
                                df[col] = pd.to_datetime(df[col], errors='coerce')
                            elif dtype_str == 'double' or dtype_str == 'float':
                                df[col] = pd.to_numeric(df[col], errors='coerce', downcast='float')
                            elif dtype_str == 'boolean':
                                df[col] = df[col].map({'TRUE': True, 'FALSE': False, True: True, False: False})
            except Exception:
                # If anything goes wrong, fall back to the original DataFrame
                df = pd.read_excel(file_path, **kwargs)

        return df

    def _load_json(self, file_path: str, **kwargs) -> pd.DataFrame:
        """Load JSON file with appropriate optimizations."""
        file_size = os.path.getsize(file_path)

        # For large files, try to use lines=True if appropriate
        if file_size > 50 * 1024 * 1024:  # Over 50MB
            with open(file_path, 'r') as f:
                first_char = f.read(1)
                if first_char != '[':
                    kwargs['lines'] = True

        return pd.read_json(file_path, **kwargs)

    def _load_parquet(self, file_path: str, **kwargs) -> pd.DataFrame:
        """Load Parquet file with optimizations."""
        return pd.read_parquet(file_path, **kwargs)

    def _infer_optimal_dtypes(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Infer optimal dtypes for DataFrame columns."""
        dtypes = {}

        for col in df.columns:
            # Skip columns with mixed types
            if df[col].dtype == 'object':
                # Check if column might be categorical
                if df[col].nunique() < len(df) * 0.5:
                    dtypes[col] = 'category'
                continue

            # For numeric columns, try to downcast
            if np.issubdtype(df[col].dtype, np.integer):
                dtypes[col] = pd.Int64Dtype()  # Use nullable integer type
            elif np.issubdtype(df[col].dtype, np.floating):
                dtypes[col] = pd.Float64Dtype()  # Use nullable float type

        return dtypes

    def _optimize_dtypes(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Optimize DataFrame memory usage by selecting appropriate data types.

        This method:
        1. Converts integers to the smallest possible integer type
        2. Converts floats to float32 where possible
        3. Converts string columns to categorical when beneficial
        4. Uses nullable dtypes for better NA handling

        Args:
            df: Input DataFrame to optimize

        Returns:
            Memory-optimized DataFrame
        """
        result = df.copy()

        # Uncomment to check how much memory has been reduced.
        # start_mem = result.memory_usage(deep=True).sum() / 1024**2

        # Process numeric columns
        numeric_columns = result.select_dtypes(include=['int', 'float']).columns
        for col in numeric_columns:
            # Get column stats
            col_series = result[col]
            col_min = col_series.min()
            col_max = col_series.max()

            # Check for presence of null values
            has_nulls = col_series.isna().any()

            # For integers
            if np.issubdtype(col_series.dtype, np.integer):
                if has_nulls:
                    # Use nullable integer type
                    if col_min >= 0:
                        if col_max <= 255:
                            result[col] = col_series.astype(pd.UInt8Dtype())
                        elif col_max <= 65535:
                            result[col] = col_series.astype(pd.UInt16Dtype())
                        elif col_max <= 4294967295:
                            result[col] = col_series.astype(pd.UInt32Dtype())
                        else:
                            result[col] = col_series.astype(pd.UInt64Dtype())
                    else:
                        if col_min >= -128 and col_max <= 127:
                            result[col] = col_series.astype(pd.Int8Dtype())
                        elif col_min >= -32768 and col_max <= 32767:
                            result[col] = col_series.astype(pd.Int16Dtype())
                        elif col_min >= -2147483648 and col_max <= 2147483647:
                            result[col] = col_series.astype(pd.Int32Dtype())
                        else:
                            result[col] = col_series.astype(pd.Int64Dtype())
                else:
                    # Use standard integer type with downcast
                    result[col] = pd.to_numeric(col_series, downcast='integer')

            # For floats
            elif np.issubdtype(col_series.dtype, np.floating):
                if has_nulls:
                    # Use nullable float type
                    result[col] = col_series.astype(pd.Float32Dtype())
                else:
                    # Check if float32 precision is sufficient
                    float32_series = col_series.astype(np.float32)
                    if (col_series == float32_series).all():
                        result[col] = float32_series
                    else:
                        result[col] = pd.to_numeric(col_series, downcast='float')

        # Process string/object columns
        object_columns = result.select_dtypes(include=['object', 'string']).columns
        for col in object_columns:
            col_series = result[col]
            num_unique = col_series.nunique()
            num_total = len(col_series)

            # Convert to categorical if beneficial
            # Use more conservative threshold for very large datasets
            if num_total > 1_000_000:
                threshold = 0.1  # 10% for large datasets
            else:
                threshold = 0.5  # 50% for smaller datasets

            if num_unique / num_total < threshold:
                result[col] = col_series.astype('category')
            elif pd.api.types.infer_dtype(col_series) == 'string':
                # Use string dtype for string columns (more efficient than object)
                result[col] = col_series.astype(pd.StringDtype())

        # Process datetime columns
        datetime_columns = result.select_dtypes(include=['datetime']).columns
        for col in datetime_columns:
            # Convert to datetime64[ns] for better memory usage
            result[col] = pd.to_datetime(result[col])

        # end_mem = result.memory_usage(deep=True).sum() / 1024**2
        # reduction = (start_mem - end_mem) / start_mem

        # print(f"Memory usage decreased from {start_mem:.2f} MB to {end_mem:.2f} MB ({reduction:.2%} reduction)")

        return result