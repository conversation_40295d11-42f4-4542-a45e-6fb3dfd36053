"""
Pandas implementation of the CLEAN framework.
"""

import pandas as pd
import numpy as np
from typing import Any, Dict, List, Tuple, Optional, Union
from datetime import datetime
import json
import hashlib
import re
from scipy import stats

from core.clean_framework.base import CleanFrameworkBase


class PandasCleanFramework(CleanFrameworkBase):
    """
    Pandas implementation of the CLEAN framework.
    """

    def conceptualize(self, data: pd.DataFrame) -> Dict[str, Any]:
        """
        Conceptualize the data by analyzing its structure, relationships, and meaning.

        Args:
            data: pandas DataFrame

        Returns:
            Dictionary containing conceptualization results
        """
        results = {
            "structure": {
                "shape": data.shape,
                "columns": data.columns.tolist(),
                "dtypes": {col: str(dtype) for col, dtype in data.dtypes.items()},
                "memory_usage": float(data.memory_usage(deep=True).sum() / (1024 * 1024)),  # MB
            },
            "summary": {
                "numeric": {},
                "categorical": {},
                "datetime": {},
                "text": {}
            },
            "relationships": {
                "correlations": {},
                "potential_keys": []
            },
            "quality_overview": {
                "completeness": 0.0,
                "uniqueness": 0.0,
                "consistency": 0.0
            }
        }

        # Analyze numeric columns
        numeric_cols = data.select_dtypes(include=[np.number]).columns.tolist()
        if numeric_cols:
            numeric_summary = data[numeric_cols].describe().to_dict()
            # Convert numpy types to Python native types
            for col, stats in numeric_summary.items():
                results["summary"]["numeric"][col] = {k: float(v) for k, v in stats.items()}

            # Calculate correlations for numeric columns
            if len(numeric_cols) > 1:
                corr_matrix = data[numeric_cols].corr().to_dict()
                # Convert to native Python types
                results["relationships"]["correlations"] = {
                    col1: {col2: float(val) for col2, val in cols.items()}
                    for col1, cols in corr_matrix.items()
                }

        # Analyze categorical columns
        cat_cols = data.select_dtypes(include=["object", "category"]).columns.tolist()
        if cat_cols:
            for col in cat_cols:
                value_counts = data[col].value_counts(dropna=False)
                results["summary"]["categorical"][col] = {
                    "unique_count": int(data[col].nunique()),
                    "top_values": {str(k): int(v) for k, v in value_counts.head(5).items()},
                    "null_count": int(data[col].isna().sum())
                }

        # Analyze datetime columns
        date_cols = data.select_dtypes(include=["datetime"]).columns.tolist()
        if date_cols:
            for col in date_cols:
                results["summary"]["datetime"][col] = {
                    "min": data[col].min().isoformat() if not pd.isna(data[col].min()) else None,
                    "max": data[col].max().isoformat() if not pd.isna(data[col].max()) else None,
                    "null_count": int(data[col].isna().sum())
                }

        # Identify potential primary keys
        for col in data.columns:
            # Use explicit comparisons to avoid "truth value of a Series is ambiguous" error
            nunique = data[col].nunique()
            count = data[col].count()
            if nunique == len(data) and count == len(data):
                results["relationships"]["potential_keys"].append(col)

        # Calculate quality metrics
        total_cells = data.size
        missing_cells = data.isna().sum().sum()
        results["quality_overview"]["completeness"] = float(1 - (missing_cells / total_cells))

        # Uniqueness - average column uniqueness
        # Calculate uniqueness for each column and handle potential division by zero
        col_uniqueness = []
        for col in data.columns:
            if len(data) > 0:
                col_uniqueness.append(data[col].nunique() / len(data))
            else:
                col_uniqueness.append(0.0)

        results["quality_overview"]["uniqueness"] = float(np.mean(col_uniqueness))

        # Consistency - placeholder (would need domain-specific rules)
        results["quality_overview"]["consistency"] = 1.0

        return results

    def locate_issues(self, data: pd.DataFrame, params: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Locate solvable data quality issues.

        Args:
            data: pandas DataFrame
            params: Optional parameters for issue detection

        Returns:
            Dictionary containing detected issues
        """
        if params is None:
            params = {}

        # Default thresholds
        missing_threshold = params.get("missing_threshold", 0.1)  # 10% missing values
        duplicate_threshold = params.get("duplicate_threshold", 0.01)  # 1% duplicate rows
        outlier_threshold = params.get("outlier_threshold", 3.0)  # 3 std deviations

        issues = {
            "missing_values": {
                "columns": {},
                "total_missing_rate": 0.0
            },
            "duplicates": {
                "count": 0,
                "rate": 0.0,
                "columns": []
            },
            "outliers": {
                "columns": {}
            },
            "inconsistent_values": {
                "columns": {}
            },
            "type_issues": {
                "columns": {}
            }
        }

        # Check for missing values
        missing_counts = data.isnull().sum()
        missing_rates = missing_counts / len(data)
        total_missing = data.isnull().sum().sum()
        total_cells = data.size

        issues["missing_values"]["total_missing_rate"] = float(total_missing / total_cells)

        for col in data.columns:
            missing_rate = missing_rates[col]
            if missing_rate > 0:
                issues["missing_values"]["columns"][col] = {
                    "count": int(missing_counts[col]),
                    "rate": float(missing_rate),
                    "exceeds_threshold": missing_rate > missing_threshold
                }

        # Check for duplicates
        duplicate_count = data.duplicated().sum()
        duplicate_rate = duplicate_count / len(data)
        issues["duplicates"]["count"] = int(duplicate_count)
        issues["duplicates"]["rate"] = float(duplicate_rate)
        issues["duplicates"]["exceeds_threshold"] = duplicate_rate > duplicate_threshold

        # Check for duplicate columns
        duplicate_cols = []
        for i, col1 in enumerate(data.columns):
            for col2 in data.columns[i+1:]:
                # Use equals method to compare Series and handle potential issues
                if data[col1].equals(data[col2]):
                    duplicate_cols.append((col1, col2))

        issues["duplicates"]["columns"] = duplicate_cols

        # Check for outliers in numeric columns
        numeric_cols = data.select_dtypes(include=[np.number]).columns
        for col in numeric_cols:
            col_data = data[col].dropna()
            if len(col_data) > 0:
                mean = col_data.mean()
                std = col_data.std()
                if std > 0:  # Avoid division by zero
                    z_scores = np.abs((col_data - mean) / std)
                    outliers = z_scores > outlier_threshold
                    outlier_count = outliers.sum()
                    if outlier_count > 0:
                        issues["outliers"]["columns"][col] = {
                            "count": int(outlier_count),
                            "rate": float(outlier_count / len(col_data)),
                            "threshold": float(outlier_threshold),
                            "indices": col_data.index[outliers].tolist()[:10]  # First 10 outlier indices
                        }

        # Check for inconsistent values in categorical columns
        cat_cols = data.select_dtypes(include=["object", "category"]).columns
        for col in cat_cols:
            # Check for mixed case values that might be the same
            values = data[col].dropna().astype(str)
            lowercase_values = values.str.lower()
            unique_count = values.nunique()
            lowercase_unique_count = lowercase_values.nunique()

            if unique_count > lowercase_unique_count:
                issues["inconsistent_values"]["columns"][col] = {
                    "issue_type": "case_inconsistency",
                    "unique_count": int(unique_count),
                    "lowercase_unique_count": int(lowercase_unique_count),
                    "examples": values.value_counts().head(5).to_dict()
                }

        # Check for potential type issues
        for col in data.columns:
            col_data = data[col].dropna()
            if col_data.empty:
                continue

            if data[col].dtype == 'object':
                # Check if column contains numeric values stored as strings
                numeric_count = pd.to_numeric(col_data, errors='coerce').notna().sum()
                if numeric_count > 0 and numeric_count / len(col_data) > 0.5:  # More than 50% can be converted to numeric
                    issues["type_issues"]["columns"][col] = {
                        "issue_type": "numeric_as_string",
                        "convertible_rate": float(numeric_count / len(col_data)),
                        "examples": col_data.head(5).tolist()
                    }

                # Check if column contains date values stored as strings
                date_count = pd.to_datetime(col_data, errors='coerce').notna().sum()
                if date_count > 0 and date_count / len(col_data) > 0.5:  # More than 50% can be converted to datetime
                    issues["type_issues"]["columns"][col] = {
                        "issue_type": "date_as_string",
                        "convertible_rate": float(date_count / len(col_data)),
                        "examples": col_data.head(5).tolist()
                    }

        return issues

    def evaluate_unsolvable(self, data: pd.DataFrame, issues: Dict[str, Any]) -> Dict[str, Any]:
        """
        Evaluate unsolvable data quality issues.

        Args:
            data: pandas DataFrame
            issues: Dictionary of detected issues

        Returns:
            Dictionary containing evaluation results
        """
        evaluation = {
            "critical_issues": [],
            "warnings": [],
            "recommendations": [],
            "data_quality_score": 100.0
        }

        # Evaluate missing values
        high_missing_cols = [
            col for col, info in issues["missing_values"]["columns"].items()
            if info["rate"] > 0.5  # More than 50% missing
        ]

        if high_missing_cols:
            evaluation["critical_issues"].append({
                "type": "high_missing_values",
                "description": f"Columns with more than 50% missing values: {', '.join(high_missing_cols)}",
                "recommendation": "Consider dropping these columns or using advanced imputation techniques."
            })

        # Evaluate data consistency
        if issues["inconsistent_values"]["columns"]:
            cols = list(issues["inconsistent_values"]["columns"].keys())
            evaluation["warnings"].append({
                "type": "inconsistent_values",
                "description": f"Inconsistent values detected in columns: {', '.join(cols)}",
                "recommendation": "Standardize values using string transformations."
            })

        # Evaluate data types
        if issues["type_issues"]["columns"]:
            for col, info in issues["type_issues"]["columns"].items():
                if info["issue_type"] == "numeric_as_string":
                    evaluation["recommendations"].append({
                        "type": "type_conversion",
                        "description": f"Column '{col}' contains numeric values stored as strings.",
                        "recommendation": "Convert to appropriate numeric type."
                    })
                elif info["issue_type"] == "date_as_string":
                    evaluation["recommendations"].append({
                        "type": "type_conversion",
                        "description": f"Column '{col}' contains date values stored as strings.",
                        "recommendation": "Convert to datetime type."
                    })

        # Evaluate overall data quality
        total_issues = (
            len(issues["missing_values"]["columns"]) +
            issues["duplicates"]["count"] +
            len(issues["outliers"]["columns"]) +
            len(issues["inconsistent_values"]["columns"]) +
            len(issues["type_issues"]["columns"])
        )

        if total_issues > 0:
            data_quality_score = max(0, 100 - (total_issues / data.size * 1000))
            evaluation["data_quality_score"] = float(data_quality_score)

            if data_quality_score < 50:
                evaluation["critical_issues"].append({
                    "type": "low_data_quality",
                    "description": f"Overall data quality score is low: {data_quality_score:.2f}/100",
                    "recommendation": "Address the identified issues before proceeding with analysis."
                })

        return evaluation

    def augment(self, data: pd.DataFrame, cleaning_plan: List[Dict[str, Any]]) -> Tuple[pd.DataFrame, Dict[str, Any]]:
        """
        Augment data quality through cleaning operations.

        Args:
            data: pandas DataFrame
            cleaning_plan: List of cleaning operations to apply

        Returns:
            Tuple containing cleaned data and cleaning report
        """
        # Create a copy of the data to avoid modifying the original
        result = data.copy()

        # Initialize cleaning report
        report = {
            "operations_applied": [],
            "rows_before": len(data),
            "columns_before": len(data.columns),
            "rows_after": 0,
            "columns_after": 0,
            "changes": {}
        }

        # Apply each cleaning operation in sequence
        for operation in cleaning_plan:
            op_type = operation.get("type")
            params = operation.get("params", {})

            try:
                if op_type == "drop_columns":
                    before_cols = result.columns.tolist()
                    result = self._drop_columns(result, **params)
                    after_cols = result.columns.tolist()

                    report["operations_applied"].append({
                        "type": op_type,
                        "params": params,
                        "columns_removed": [col for col in before_cols if col not in after_cols]
                    })

                elif op_type == "fill_missing":
                    before_missing = result.isnull().sum().sum()
                    result = self._fill_missing(result, **params)
                    after_missing = result.isnull().sum().sum()

                    report["operations_applied"].append({
                        "type": op_type,
                        "params": params,
                        "values_filled": int(before_missing - after_missing)
                    })

                elif op_type == "drop_missing":
                    before_rows = len(result)
                    result = self._drop_missing(result, **params)
                    after_rows = len(result)

                    report["operations_applied"].append({
                        "type": op_type,
                        "params": params,
                        "rows_removed": int(before_rows - after_rows)
                    })

                elif op_type == "drop_duplicates":
                    before_rows = len(result)
                    result = self._drop_duplicates(result, **params)
                    after_rows = len(result)

                    report["operations_applied"].append({
                        "type": op_type,
                        "params": params,
                        "rows_removed": int(before_rows - after_rows)
                    })

                elif op_type == "fix_data_types":
                    before_dtypes = {col: str(dtype) for col, dtype in result.dtypes.items()}
                    result = self._fix_data_types(result, **params)
                    after_dtypes = {col: str(dtype) for col, dtype in result.dtypes.items()}

                    changed_dtypes = {
                        col: {"before": before_dtypes[col], "after": after_dtypes[col]}
                        for col in before_dtypes
                        if col in after_dtypes and before_dtypes[col] != after_dtypes[col]
                    }

                    report["operations_applied"].append({
                        "type": op_type,
                        "params": params,
                        "changed_dtypes": changed_dtypes
                    })

                elif op_type == "remove_outliers":
                    before_rows = len(result)
                    result = self._remove_outliers(result, **params)
                    after_rows = len(result)

                    report["operations_applied"].append({
                        "type": op_type,
                        "params": params,
                        "rows_removed": int(before_rows - after_rows)
                    })

                elif op_type == "standardize_values":
                    result = self._standardize_values(result, **params)

                    report["operations_applied"].append({
                        "type": op_type,
                        "params": params
                    })

                else:
                    raise ValueError(f"Unsupported operation type: {op_type}")

            except Exception as e:
                # Log the error but continue with other operations
                report["operations_applied"].append({
                    "type": op_type,
                    "params": params,
                    "error": str(e)
                })

        # Update final stats
        report["rows_after"] = len(result)
        report["columns_after"] = len(result.columns)

        # Calculate overall changes
        report["changes"] = {
            "rows_change": report["rows_after"] - report["rows_before"],
            "columns_change": report["columns_after"] - report["columns_before"],
            "rows_change_percent": (report["rows_after"] - report["rows_before"]) / report["rows_before"] * 100 if report["rows_before"] > 0 else 0,
            "columns_change_percent": (report["columns_after"] - report["columns_before"]) / report["columns_before"] * 100 if report["columns_before"] > 0 else 0
        }

        return result, report

    def document(self,
                original_data: pd.DataFrame,
                cleaned_data: pd.DataFrame,
                conceptualization: Dict[str, Any],
                issues: Dict[str, Any],
                evaluation: Dict[str, Any],
                cleaning_report: Dict[str, Any]) -> Dict[str, Any]:
        """
        Document the entire cleaning process.

        Args:
            original_data: Original pandas DataFrame
            cleaned_data: Cleaned pandas DataFrame
            conceptualization: Results from the conceptualize step
            issues: Results from the locate_issues step
            evaluation: Results from the evaluate_unsolvable step
            cleaning_report: Report from the augment step

        Returns:
            Dictionary containing comprehensive documentation of the cleaning process
        """
        # Generate a unique identifier for this cleaning process
        process_id = hashlib.md5(
            f"{datetime.now().isoformat()}-{original_data.shape}".encode()
        ).hexdigest()

        documentation = {
            "process_id": process_id,
            "timestamp": datetime.now().isoformat(),
            "data_summary": {
                "original": {
                    "shape": original_data.shape,
                    "columns": original_data.columns.tolist(),
                    "memory_usage_mb": float(original_data.memory_usage(deep=True).sum() / (1024 * 1024))
                },
                "cleaned": {
                    "shape": cleaned_data.shape,
                    "columns": cleaned_data.columns.tolist(),
                    "memory_usage_mb": float(cleaned_data.memory_usage(deep=True).sum() / (1024 * 1024))
                },
                "changes": {
                    "rows_change": cleaned_data.shape[0] - original_data.shape[0],
                    "columns_change": cleaned_data.shape[1] - original_data.shape[1],
                    "rows_change_percent": (cleaned_data.shape[0] - original_data.shape[0]) / original_data.shape[0] * 100 if original_data.shape[0] > 0 else 0,
                    "columns_change_percent": (cleaned_data.shape[1] - original_data.shape[1]) / original_data.shape[1] * 100 if original_data.shape[1] > 0 else 0
                }
            },
            "conceptualization": conceptualization,
            "issues_detected": issues,
            "evaluation": evaluation,
            "cleaning_report": cleaning_report,
            "quality_improvement": {
                "before": self._calculate_quality_score(original_data),
                "after": self._calculate_quality_score(cleaned_data),
                "improvement": 0.0  # Will be calculated below
            }
        }

        # Calculate quality improvement
        before_score = documentation["quality_improvement"]["before"]
        after_score = documentation["quality_improvement"]["after"]
        documentation["quality_improvement"]["improvement"] = after_score - before_score

        return documentation

    def _calculate_quality_score(self, data: pd.DataFrame) -> float:
        """
        Calculate a data quality score based on various metrics.

        Args:
            data: pandas DataFrame

        Returns:
            Quality score between 0 and 100
        """
        # Initialize score components
        completeness_score = 0.0
        uniqueness_score = 0.0
        consistency_score = 0.0

        # Completeness: percentage of non-missing values
        total_cells = data.size
        missing_cells = data.isnull().sum().sum()
        completeness_score = 100 * (1 - (missing_cells / total_cells)) if total_cells > 0 else 0

        # Uniqueness: penalize for duplicate rows
        duplicate_rate = data.duplicated().mean()
        uniqueness_score = 100 * (1 - duplicate_rate)

        # Consistency: check for mixed data types in columns
        consistency_issues = 0
        for col in data.columns:
            if data[col].dtype == 'object':
                # Check if column contains mixed numeric and non-numeric values
                values = data[col].dropna()
                if len(values) > 0:
                    numeric_count = pd.to_numeric(values, errors='coerce').notna().sum()
                    if 0 < numeric_count < len(values):
                        consistency_issues += 1

        consistency_score = 100 * (1 - (consistency_issues / len(data.columns))) if len(data.columns) > 0 else 0

        # Calculate overall score (weighted average)
        overall_score = (0.4 * completeness_score + 0.3 * uniqueness_score + 0.3 * consistency_score)

        return float(overall_score)

    def _drop_columns(self, data: pd.DataFrame, columns: List[str]) -> pd.DataFrame:
        """Drop specified columns from the DataFrame."""
        return data.drop(columns=columns, errors='ignore')

    def _fill_missing(self, data: pd.DataFrame, columns: Union[List[str], str],
                     method: str = "mean", value: Optional[Any] = None) -> pd.DataFrame:
        """Fill missing values in specified columns."""
        result = data.copy()

        # Determine which columns to process
        if columns == "all":
            target_columns = data.columns
        else:
            target_columns = columns

        # Apply the specified method
        for col in target_columns:
            if col not in data.columns:
                continue

            if method == "mean" and pd.api.types.is_numeric_dtype(data[col]):
                result[col] = data[col].fillna(data[col].mean())
            elif method == "median" and pd.api.types.is_numeric_dtype(data[col]):
                result[col] = data[col].fillna(data[col].median())
            elif method == "mode":
                if not data[col].empty and data[col].mode().size > 0:
                    result[col] = data[col].fillna(data[col].mode()[0])
            elif method == "constant":
                result[col] = data[col].fillna(value)
            elif method == "ffill":
                result[col] = data[col].fillna(method='ffill')
            elif method == "bfill":
                result[col] = data[col].fillna(method='bfill')

        return result

    def _drop_missing(self, data: pd.DataFrame, how: str = "any", threshold: Optional[float] = None) -> pd.DataFrame:
        """
        Drop rows with missing values.

        Args:
            data: pandas DataFrame
            how: How to drop - 'any' (drop if any value is missing) or 'all' (drop if all values are missing)
            threshold: Optional threshold for dropping rows (e.g., 0.5 to drop rows with more than 50% missing values)

        Returns:
            DataFrame with rows dropped
        """
        if threshold is not None:
            # Drop rows with more than threshold missing values
            return data.dropna(thresh=int((1-threshold) * data.shape[1]))
        else:
            # Use standard dropna with how parameter
            return data.dropna(how=how)

    def _drop_duplicates(self, data: pd.DataFrame, subset: Optional[List[str]] = None,
                        keep: str = "first") -> pd.DataFrame:
        """
        Drop duplicate rows.

        Args:
            data: pandas DataFrame
            subset: Optional list of columns to consider for identifying duplicates
            keep: Which duplicates to keep - 'first', 'last', or False (drop all duplicates)

        Returns:
            DataFrame with duplicates removed
        """
        return data.drop_duplicates(subset=subset, keep=keep)

    def _fix_data_types(self, data: pd.DataFrame, columns: List[str],
                       target_types: Dict[str, str]) -> pd.DataFrame:
        """
        Convert columns to appropriate data types.

        Args:
            data: pandas DataFrame
            columns: List of columns to convert
            target_types: Dictionary mapping column names to target types ('numeric', 'datetime', 'category', etc.)

        Returns:
            DataFrame with converted data types
        """
        result = data.copy()

        for col in columns:
            if col not in data.columns:
                continue

            target_type = target_types.get(col)
            if not target_type:
                continue

            try:
                if target_type == "numeric":
                    result[col] = pd.to_numeric(data[col], errors='coerce')
                elif target_type == "datetime":
                    result[col] = pd.to_datetime(data[col], errors='coerce')
                elif target_type == "category":
                    result[col] = data[col].astype('category')
                elif target_type == "string":
                    result[col] = data[col].astype(str)
                elif target_type == "boolean":
                    # Try to convert to boolean, handling various string representations
                    result[col] = data[col].map({
                        'true': True, 'True': True, '1': True, 1: True, 'yes': True, 'Yes': True,
                        'false': False, 'False': False, '0': False, 0: False, 'no': False, 'No': False
                    }).astype('boolean')
            except Exception:
                # If conversion fails, keep the original column
                pass

        return result

    def _remove_outliers(self, data: pd.DataFrame, columns: List[str],
                        method: str = "zscore", threshold: float = 3.0) -> pd.DataFrame:
        """
        Remove outliers from specified columns.

        Args:
            data: pandas DataFrame
            columns: List of columns to check for outliers
            method: Method to use - 'zscore' or 'iqr'
            threshold: Threshold for outlier detection (e.g., 3.0 for z-score, 1.5 for IQR)

        Returns:
            DataFrame with outliers removed
        """
        result = data.copy()
        mask = pd.Series(True, index=data.index)

        for col in columns:
            if col not in data.columns or not pd.api.types.is_numeric_dtype(data[col]):
                continue

            col_data = data[col].dropna()

            if method == "zscore":
                # Z-score method
                if len(col_data) > 0:
                    mean = col_data.mean()
                    std = col_data.std()
                    if std > 0:  # Avoid division by zero
                        z_scores = np.abs((data[col] - mean) / std)
                        mask = mask & (z_scores <= threshold)

            elif method == "iqr":
                # IQR method
                q1 = data[col].quantile(0.25)
                q3 = data[col].quantile(0.75)
                iqr = q3 - q1
                lower_bound = q1 - threshold * iqr
                upper_bound = q3 + threshold * iqr
                mask = mask & ((data[col] >= lower_bound) | (data[col].isna())) & ((data[col] <= upper_bound) | (data[col].isna()))

        return result[mask]

    def _standardize_values(self, data: pd.DataFrame, columns: List[str],
                           standardization_map: Optional[Dict[str, Dict[str, str]]] = None,
                           case: Optional[str] = None) -> pd.DataFrame:
        """
        Standardize values in categorical columns.

        Args:
            data: pandas DataFrame
            columns: List of columns to standardize
            standardization_map: Optional dictionary mapping original values to standardized values
            case: Optional case transformation ('lower', 'upper', 'title')

        Returns:
            DataFrame with standardized values
        """
        result = data.copy()

        for col in columns:
            if col not in data.columns:
                continue

            # Apply standardization map if provided
            if standardization_map and col in standardization_map:
                result[col] = result[col].map(standardization_map[col]).fillna(result[col])

            # Apply case transformation if specified
            if case and pd.api.types.is_string_dtype(result[col]):
                if case == "lower":
                    result[col] = result[col].str.lower()
                elif case == "upper":
                    result[col] = result[col].str.upper()
                elif case == "title":
                    result[col] = result[col].str.title()

        return result
