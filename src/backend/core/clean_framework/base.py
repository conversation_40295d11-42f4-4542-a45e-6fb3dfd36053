"""
Base class for the CLEAN framework implementation.
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, List, Tuple, Optional


class CleanFrameworkBase(ABC):
    """
    Base class for the CLEAN framework implementation.
    
    The CLEAN framework consists of five steps:
    1. Conceptualize the data: Understand the data structure, meaning, and relationships
    2. Locate solvable issues: Identify data quality issues that can be addressed
    3. Evaluate unsolvable issues: Assess issues that cannot be fully resolved
    4. Augment the data: Enhance data quality through transformations and enrichment
    5. Note and document: Document all cleaning steps and decisions
    """
    
    @abstractmethod
    def conceptualize(self, data: Any) -> Dict[str, Any]:
        """
        Conceptualize the data by analyzing its structure, relationships, and meaning.
        
        Args:
            data: Data in the engine's native format
            
        Returns:
            Dictionary containing conceptualization results including:
            - Structure information (shape, columns, data types)
            - Summary statistics
            - Data relationships
            - Data quality overview
        """
        pass
    
    @abstractmethod
    def locate_issues(self, data: Any, params: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Locate solvable data quality issues.
        
        Args:
            data: Data in the engine's native format
            params: Optional parameters for issue detection
            
        Returns:
            Dictionary containing detected issues including:
            - Missing values
            - Duplicates
            - Outliers
            - Inconsistent values
            - Type issues
            - Format issues
        """
        pass
    
    @abstractmethod
    def evaluate_unsolvable(self, data: Any, issues: Dict[str, Any]) -> Dict[str, Any]:
        """
        Evaluate unsolvable data quality issues.
        
        Args:
            data: Data in the engine's native format
            issues: Dictionary of detected issues
            
        Returns:
            Dictionary containing evaluation results including:
            - Critical issues that cannot be automatically resolved
            - Warnings about potential data quality problems
            - Recommendations for manual intervention
            - Overall data quality score
        """
        pass
    
    @abstractmethod
    def augment(self, data: Any, cleaning_plan: List[Dict[str, Any]]) -> Tuple[Any, Dict[str, Any]]:
        """
        Augment data quality through cleaning operations.
        
        Args:
            data: Data in the engine's native format
            cleaning_plan: List of cleaning operations to apply
            
        Returns:
            Tuple containing:
            - Cleaned data in the engine's native format
            - Cleaning report with details of applied operations
        """
        pass
    
    @abstractmethod
    def document(self, 
                original_data: Any, 
                cleaned_data: Any, 
                conceptualization: Dict[str, Any],
                issues: Dict[str, Any],
                evaluation: Dict[str, Any],
                cleaning_report: Dict[str, Any]) -> Dict[str, Any]:
        """
        Document the entire cleaning process.
        
        Args:
            original_data: Original data in the engine's native format
            cleaned_data: Cleaned data in the engine's native format
            conceptualization: Results from the conceptualize step
            issues: Results from the locate_issues step
            evaluation: Results from the evaluate_unsolvable step
            cleaning_report: Report from the augment step
            
        Returns:
            Dictionary containing comprehensive documentation of the cleaning process
        """
        pass
    
    def process(self, data: Any, params: Optional[Dict[str, Any]] = None) -> Tuple[Any, Dict[str, Any]]:
        """
        Process data through the complete CLEAN framework.
        
        Args:
            data: Data in the engine's native format
            params: Optional parameters for the cleaning process
            
        Returns:
            Tuple containing:
            - Cleaned data in the engine's native format
            - Documentation of the entire cleaning process
        """
        if params is None:
            params = {}
        
        # Step 1: Conceptualize the data
        conceptualization = self.conceptualize(data)
        
        # Step 2: Locate solvable issues
        issues = self.locate_issues(data, params.get("locate_params"))
        
        # Step 3: Evaluate unsolvable issues
        evaluation = self.evaluate_unsolvable(data, issues)
        
        # Step 4: Augment the data
        cleaning_plan = params.get("cleaning_plan", self.generate_cleaning_plan(issues, evaluation))
        cleaned_data, cleaning_report = self.augment(data, cleaning_plan)
        
        # Step 5: Note and document
        documentation = self.document(
            data, cleaned_data, conceptualization, issues, evaluation, cleaning_report
        )
        
        return cleaned_data, documentation
    
    def generate_cleaning_plan(self, issues: Dict[str, Any], evaluation: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Generate a cleaning plan based on identified issues and evaluation.
        
        Args:
            issues: Dictionary of detected issues
            evaluation: Dictionary containing evaluation results
            
        Returns:
            List of cleaning operations to apply
        """
        # This is a default implementation that can be overridden by subclasses
        cleaning_plan = []
        
        # Handle missing values
        if issues.get("missing_values", {}).get("columns"):
            missing_cols = list(issues["missing_values"]["columns"].keys())
            cleaning_plan.append({
                "type": "fill_missing",
                "params": {
                    "columns": missing_cols,
                    "method": "mean"  # Default method
                }
            })
        
        # Handle duplicates
        if issues.get("duplicates", {}).get("count", 0) > 0:
            cleaning_plan.append({
                "type": "drop_duplicates",
                "params": {}
            })
        
        # Handle type issues
        if issues.get("type_issues", {}).get("columns"):
            type_fixes = {}
            for col, info in issues["type_issues"]["columns"].items():
                if info["issue_type"] == "numeric_as_string":
                    type_fixes[col] = "numeric"
                elif info["issue_type"] == "date_as_string":
                    type_fixes[col] = "datetime"
            
            if type_fixes:
                cleaning_plan.append({
                    "type": "fix_data_types",
                    "params": {
                        "columns": list(type_fixes.keys()),
                        "target_types": type_fixes
                    }
                })
        
        return cleaning_plan
