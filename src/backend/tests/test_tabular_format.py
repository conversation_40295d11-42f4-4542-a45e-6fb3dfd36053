"""
Test the tabular format CSV loading functionality.
"""

import os
import sys
import tempfile
import pandas as pd

# Add the src directory to the path so we can import modules
sys.path.append('/Users/<USER>/Documents/Projects/2025/data_analysis_tool')

# Import the PandasIngestion class
from src.backend.core.ingestion.pandas_ingestion import PandasIngestion

def create_test_csv():
    """Create a test CSV file with tabular format."""
    content = """,result,table,_start,_stop,_time,_value,_field,_measurement,hardware
0,,0,2025-05-14T02:15:07.913600778Z,2025-05-14T14:15:07.913600778Z,2025-05-14T02:15:08.555709105Z,0,value,ac_over_voltage_alarm,hvac
1,,0,2025-05-14T02:15:07.913600778Z,2025-05-14T14:15:07.913600778Z,2025-05-14T02:15:11.081298173Z,0,value,ac_over_voltage_alarm,hvac
2,,0,2025-05-14T02:15:07.913600778Z,2025-05-14T14:15:07.913600778Z,2025-05-14T02:15:13.596614972Z,0,value,ac_over_voltage_alarm,hvac"""

    with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.csv') as f:
        f.write(content)
        return f.name

def test_tabular_format_csv_loading():
    """Test loading a tabular format CSV file."""
    # Create a test file
    file_path = create_test_csv()

    try:
        # Initialize the ingestion class
        ingestion = PandasIngestion()

        # Load the file
        df = ingestion.load_data(file_path, 'csv')

        # Print the DataFrame for debugging
        print("DataFrame shape:", df.shape)
        print("DataFrame columns:", df.columns.tolist())
        print("DataFrame dtypes:", df.dtypes.to_dict())

        # Print first row for debugging
        print("\nFirst DataFrame row:")
        print(df.iloc[0].to_dict())

        # Verify the data was loaded correctly
        assert len(df) == 3  # Three data rows
        assert df.shape[1] >= 9  # At least 9 columns

        # Check that data types were applied correctly
        assert pd.api.types.is_datetime64_any_dtype(df['_time'])  # _time should be datetime (with or without timezone)
        assert pd.api.types.is_numeric_dtype(df['_value'])  # _value should be numeric

        # Check some values
        assert df.iloc[0]['_field'] == 'value'
        assert df.iloc[0]['_measurement'] == 'ac_over_voltage_alarm'
        assert df.iloc[0]['hardware'] == 'hvac'

    finally:
        # Clean up
        os.unlink(file_path)

if __name__ == "__main__":
    # Run the test
    test_tabular_format_csv_loading()
    print("All tests passed!")
